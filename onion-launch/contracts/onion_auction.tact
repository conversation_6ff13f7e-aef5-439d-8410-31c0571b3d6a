import "@stdlib/ownable";
import "@stdlib/stoppable";
import "@stdlib/deploy";
import "./jetton/JettonWallet";
import "./user_purchase";

// Error codes for better debugging
const ERROR_AUCTION_NOT_ACTIVE: Int = 551001;
const ERROR_AUCTION_NOT_STARTED: Int = 551002;
const ERROR_AUCTION_ENDED: Int = 551003;
const ERROR_INVALID_CURRENCY: Int = 551004;
const ERROR_AMOUNT_BELOW_MINIMUM: Int = 551005;
const ERROR_INSUFFICIENT_TOKENS: Int = 551006;
const ERROR_USDT_NOT_CONFIGURED: Int = 551007;
const ERROR_INVALID_USDT_WALLET: Int = 551008;
const ERROR_SIGNING_KEY_NOT_SET: Int = 551009;
const ERROR_SIGNATURE_EXPIRED: Int = 551010;
const ERROR_FUTURE_TIMESTAMP: Int = 551011;
const ERROR_NONCE_ALREADY_USED: Int = 551012;
const ERROR_INVALID_SIGNATURE: Int = 551013;
const ERROR_CURRENCY_MISMATCH: Int = 551014;
const ERROR_AMOUNT_MISMATCH: Int = 551015;
const ERROR_USER_MISMATCH: Int = 551016;
const ERROR_UNAUTHORIZED_REFUND: Int = 551017;
const ERROR_MIN_PURCHASE_INVALID: Int = 551018;
const ERROR_WITHDRAWAL_NOT_ALLOWED: Int = 551019;
const ERROR_INSUFFICIENT_BALANCE: Int = 551020;
const ERROR_INVALID_WITHDRAWAL_ADDRESS: Int = 551021;

// Op codes for messages
const OP_PURCHASE: Int = 0xeb9a8c4c; // 3954698588
const OP_START_AUCTION: Int = 0x408b8b11; // 1082453649
const OP_UPDATE_ROUND: Int = 0xaa8b8b89; // 2860888489
const OP_SET_USDT_ADDRESS: Int = 0xa1234afe; // 2704015070
const OP_PURCHASE_WITH_SIGNATURE: Int = 0x5ae22804; // 1524770820
const OP_SET_SIGNING_KEY: Int = 0xc5a5c5fe; // 3314411774
const OP_SET_MIN_PURCHASE: Int = 0xca1ca1c8; // 3391141464
const OP_WITHDRAW_TON: Int = 0xd1d1d1d1; // 3520061905
const OP_WITHDRAW_USDT: Int = 0xd2d2d2d2; // 3537904338
const OP_SET_TREASURY: Int = 0xd3d3d3d3; // 3555746771

// Messages with explicit op codes
message(OP_PURCHASE) Purchase {
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
}

message(OP_START_AUCTION) StartAuction {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    initial_price: Int as coins;
}

message(OP_UPDATE_ROUND) UpdateRound {
    new_price: Int as coins;
    round_number: Int as uint32;
}

message(OP_SET_USDT_ADDRESS) SetUSDTAddress {
    usdt_master: Address;
    usdt_wallet: Address; // The USDT wallet address for this contract
}

// New signature verification messages
message(OP_PURCHASE_WITH_SIGNATURE) PurchaseWithSignature {
    calculation: PurchaseCalculation;
    signature: Slice; // Server signature
}

message(OP_SET_SIGNING_KEY) SetSigningKey {
    public_key: Int as uint256; // Server's public key for signature verification
}

message(OP_SET_MIN_PURCHASE) SetMinPurchase {
    min_purchase: Int as coins; // New minimum purchase amount
}

// Fund withdrawal messages
message(OP_WITHDRAW_TON) WithdrawTON {
    amount: Int as coins; // Amount to withdraw (0 = withdraw all)
    destination: Address; // Destination wallet address
}

message(OP_WITHDRAW_USDT) WithdrawUSDT {
    amount: Int as coins; // Amount to withdraw (0 = withdraw all)
    destination: Address; // Destination wallet address
}

message(OP_SET_TREASURY) SetTreasury {
    treasury_address: Address; // Treasury wallet address for automatic withdrawals
}

// Structs
struct AuctionConfig {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    total_supply: Int as coins;
    refund_fee_percent: Int as uint8; // 5% = 5
}

// Round statistics tracking
struct RoundStats {
    round_number: Int as uint32;
    start_time: Int as uint64;
    end_time: Int as uint64;
    price: Int as coins;
    total_raised_ton: Int as coins;
    total_raised_usdt: Int as coins;
    tokens_sold: Int as coins;
    purchase_count: Int as uint32;
    unique_users: Int as uint32;
    refund_count: Int as uint32;
    refunded_amount_ton: Int as coins;
    refunded_amount_usdt: Int as coins;
}

struct USDTConfig {
    master_address: Address;
    wallet_address: Address?;
    decimals: Int as uint8; // USDT has 6 decimals
}

// New struct for signature verification
struct PurchaseCalculation {
    user: Address;
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    tokens_to_receive: Int as coins;
    current_price: Int as coins;
    current_round: Int as uint32;
    timestamp: Int as uint64;
    nonce: Int as uint64; // Anti-replay protection
}

// Struct for parsed purchase data from forward_payload
struct ParsedPurchaseData {
    calculation: PurchaseCalculation;
    signature: Slice;
}

// Main Auction Contract
contract OnionAuction with Ownable, Stoppable, Deployable {
    
    // State variables
    owner: Address;
    stopped: Bool;

    auction_config: AuctionConfig;
    current_round: Int as uint32;
    current_price: Int as coins;
    total_raised: Int as coins;
    total_tokens_sold: Int as coins;
    auction_status: Int as uint8; // 0=pending, 1=active, 2=ended_success, 3=ended_failure

    // USDT configuration
    usdt_config: USDTConfig?;
    total_raised_usdt: Int as coins; // Total USDT raised (in USDT units)

    // Counters
    purchase_count: Int as uint32;

    // Round tracking - only store round statistics in main contract
    round_stats: map<Int, RoundStats>; // round_number -> RoundStats
    // User participation tracking is now handled by individual UserPurchase contracts

    // Signature verification
    signing_public_key: Int as uint256; // Server's public key for signature verification
    used_nonces: map<Int, Bool>; // Track used nonces to prevent replay attacks
    signature_timeout: Int as uint64; // Signature validity window in seconds

    // Configurable parameters
    min_purchase: Int as coins; // Minimum purchase amount (configurable)

    // Treasury management
    treasury_address: Address?; // Treasury wallet for automatic fund collection

    // Constants
    const ROUND_DURATION: Int = 3600; // 1 hour in seconds
    const PRICE_INCREMENT: Int = ton("0.01"); // 0.01 TON per round
    const SIGNATURE_TIMEOUT: Int = 300; // 5 minutes signature validity
    
    init(
        owner: Address,
        start_time: Int,
        end_time: Int,
        soft_cap: Int,
        hard_cap: Int,
        total_supply: Int
    ) {
        self.owner = owner;
        self.stopped = false;

        self.auction_config = AuctionConfig{
            start_time: start_time,
            end_time: end_time,
            soft_cap: soft_cap,
            hard_cap: hard_cap,
            total_supply: total_supply,
            refund_fee_percent: 5
        };

        self.current_round = 1;
        self.current_price = ton("0.1"); // Initial price 0.1 TON per token
        self.total_raised = 0;
        self.total_tokens_sold = 0;
        self.auction_status = 0; // pending
        self.purchase_count = 0;

        // Initialize USDT config as null
        self.usdt_config = null;
        self.total_raised_usdt = 0;

        // Initialize signature verification
        self.signing_public_key = 0; // Will be set by owner
        self.signature_timeout = self.SIGNATURE_TIMEOUT;

        // Initialize configurable parameters
        self.min_purchase = ton("0.1"); // Default minimum purchase amount

        // Initialize treasury management
        self.treasury_address = null; // No treasury set initially

        // Initialize round tracking
        self.initializeFirstRound();
    }
    
    
    // Deploy message handler
    receive("Deploy") {
        // Handle deployment - no special logic needed for this contract
    }
    
    // Set USDT address (only owner)
    receive(msg: SetUSDTAddress) {
        self.requireOwner();
        self.usdt_config = USDTConfig{
            master_address: msg.usdt_master,
            wallet_address: msg.usdt_wallet,
            decimals: 6 // USDT has 6 decimals
        };
    }

    // Set signing public key (only owner)
    receive(msg: SetSigningKey) {
        self.requireOwner();
        self.signing_public_key = msg.public_key;
    }

    // Set minimum purchase amount (only owner)
    receive(msg: SetMinPurchase) {
        self.requireOwner();
        throwUnless(ERROR_MIN_PURCHASE_INVALID, msg.min_purchase > 0);
        self.min_purchase = msg.min_purchase;
    }

    // Set treasury address (only owner)
    receive(msg: SetTreasury) {
        self.requireOwner();
        self.treasury_address = msg.treasury_address;
    }

    // Withdraw TON (only owner)
    receive(msg: WithdrawTON) {
        self.requireOwner();
        throwUnless(ERROR_WITHDRAWAL_NOT_ALLOWED, self.auction_status == 2); // Only after successful auction

        let withdrawal_amount: Int = msg.amount;
        if (withdrawal_amount == 0) {
            // Withdraw all available TON
            withdrawal_amount = self.total_raised;
        }

        throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount <= self.total_raised);
        throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount > 0);

        // Update total raised
        self.total_raised -= withdrawal_amount;

        // Send TON to destination
        send(SendParameters{
            to: msg.destination,
            value: withdrawal_amount,
            mode: SendIgnoreErrors,
            bounce: false,
            body: "TON withdrawal from OnionAuction".asComment()
        });
    }

    // Withdraw USDT (only owner)
    receive(msg: WithdrawUSDT) {
        self.requireOwner();
        throwUnless(ERROR_WITHDRAWAL_NOT_ALLOWED, self.auction_status == 2); // Only after successful auction
        throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);

        let usdt_config: USDTConfig = self.usdt_config!!;
        throwUnless(ERROR_USDT_NOT_CONFIGURED, usdt_config.wallet_address != null);

        let withdrawal_amount: Int = msg.amount;
        if (withdrawal_amount == 0) {
            // Withdraw all available USDT
            withdrawal_amount = self.total_raised_usdt;
        }

        throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount <= self.total_raised_usdt);
        throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount > 0);

        // Update total raised USDT
        self.total_raised_usdt -= withdrawal_amount;

        // Send USDT via jetton transfer
        send(SendParameters{
            to: usdt_config.wallet_address!!,
            value: ton("0.1"), // Gas for jetton transfer
            mode: SendIgnoreErrors,
            bounce: false,
            body: JettonTransfer{
                query_id: 0,
                amount: withdrawal_amount,
                destination: msg.destination,
                response_destination: myAddress(),
                custom_payload: null,
                forward_ton_amount: 1, // 1 unit for notification
                forward_payload: "USDT withdrawal from OnionAuction".asComment()
            }.toCell()
        });
    }

    // Start auction
    receive(msg: StartAuction) {
        self.requireOwner();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 0);

        self.auction_config.start_time = msg.start_time;
        self.auction_config.end_time = msg.end_time;
        self.auction_config.soft_cap = msg.soft_cap;
        self.auction_config.hard_cap = msg.hard_cap;
        self.current_price = msg.initial_price;
        self.auction_status = 1; // active

        // Update first round with actual start time and price
        let first_round: RoundStats? = self.round_stats.get(1);
        if (first_round != null) {
            let stats: RoundStats = first_round!!;
            let updated_stats: RoundStats = RoundStats{
                round_number: stats.round_number,
                start_time: msg.start_time,
                end_time: msg.start_time + self.ROUND_DURATION,
                price: msg.initial_price,
                total_raised_ton: stats.total_raised_ton,
                total_raised_usdt: stats.total_raised_usdt,
                tokens_sold: stats.tokens_sold,
                purchase_count: stats.purchase_count,
                unique_users: stats.unique_users,
                refund_count: stats.refund_count,
                refunded_amount_ton: stats.refunded_amount_ton,
                refunded_amount_usdt: stats.refunded_amount_usdt
            };
            self.round_stats.set(1, updated_stats);
        }
    }
    
    // Purchase tokens with TON
    receive(msg: Purchase) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);
        throwUnless(ERROR_INVALID_CURRENCY, msg.currency == 0);
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, msg.amount >= self.min_purchase);

        let current_time: Int = now();
        self.updateCurrentRound(current_time);

        // Calculate tokens to receive
        let tokens_to_receive: Int = (msg.amount * ton("1")) / self.current_price;

        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised += msg.amount;
        self.total_tokens_sold += tokens_to_receive;
        self.purchase_count += 1;

        // Update round statistics
        self.updateRoundStatsForPurchase(sender(), msg.amount, tokens_to_receive, 0);

        // Check if hard cap reached
        if (self.total_raised >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(sender(), msg.amount, tokens_to_receive, 0, 0, 0); // Direct purchase, no nonce
    }

    // Handle USDT transfers via Jetton Transfer Notification
    receive(msg: JettonTransferNotification) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);

        // Verify this is from our USDT jetton wallet
        throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
        let usdt_config: USDTConfig = self.usdt_config!!;

        // Get expected USDT wallet address for this contract
        let expected_wallet: Address = self.getJettonWalletAddress(usdt_config.master_address, myAddress());
        throwUnless(ERROR_INVALID_USDT_WALLET, sender() == expected_wallet);

        // Check if forward_payload contains signature verification data
        if (msg.forward_payload != null) {
            // Handle USDT purchase with signature verification
            self.handleUSDTWithSignature(msg);
        } else {
            // Handle direct USDT purchase (backward compatibility)
            self.handleDirectUSDTPurchase(msg);
        }
    }

    // Purchase with signature verification (new method)
    receive(msg: PurchaseWithSignature) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);
        throwUnless(ERROR_SIGNING_KEY_NOT_SET, self.signing_public_key != 0);

        let calc: PurchaseCalculation = msg.calculation;

        // Verify signature timestamp is within valid window
        let current_time: Int = now();
        throwUnless(ERROR_SIGNATURE_EXPIRED, current_time - calc.timestamp <= self.signature_timeout);
        throwUnless(ERROR_FUTURE_TIMESTAMP, calc.timestamp <= current_time);

        // Check nonce to prevent replay attacks
        throwUnless(ERROR_NONCE_ALREADY_USED, self.used_nonces.get(calc.nonce) == null);

        // Verify the signature
        let data_hash: Int = self.hashPurchaseCalculation(calc);
        throwUnless(ERROR_INVALID_SIGNATURE, checkSignature(data_hash, msg.signature, self.signing_public_key));

        // Mark nonce as used
        self.used_nonces.set(calc.nonce, true);

        // Verify calculation data matches sender
        throwUnless(ERROR_USER_MISMATCH, calc.user == sender());
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, calc.amount >= self.min_purchase);
        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + calc.tokens_to_receive <= self.auction_config.total_supply);

        // Update state based on currency
        if (calc.currency == 0) {
            // TON purchase
            self.total_raised += calc.amount;
        } else if (calc.currency == 1) {
            // USDT purchase
            throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
            self.total_raised_usdt += calc.amount;
        } else {
            throwUnless(ERROR_INVALID_CURRENCY, false);
        }

        self.total_tokens_sold += calc.tokens_to_receive;
        self.purchase_count += 1;

        // Update round statistics
        self.updateRoundStatsForPurchase(calc.user, calc.amount, calc.tokens_to_receive, calc.currency);

        // Check if hard cap reached
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(calc.user, calc.amount, calc.tokens_to_receive, calc.currency, 1, calc.nonce); // Signature verified purchase
    }

    // Process refund request
    receive(msg: ProcessRefund) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);

        // Calculate expected user purchase contract address
        let expected_user_purchase_addr: Address = contractAddress(self.getUserPurchaseInit(msg.user));
        throwUnless(ERROR_UNAUTHORIZED_REFUND, expected_user_purchase_addr == sender());

        let fee: Int = (msg.amount * self.auction_config.refund_fee_percent) / 100;
        let refund_amount: Int = msg.amount - fee;

        // Update round statistics for refund
        self.updateRoundStatsForRefund(msg.round_number, msg.amount, msg.currency);

        if (msg.currency == 0) {
            // TON refund
            self.total_raised -= msg.amount;

            // Send TON refund to user
            send(SendParameters{
                to: msg.user,
                value: refund_amount,
                mode: SendIgnoreErrors,
                bounce: false,
                body: "TON refund processed".asComment()
            });
        } else if (msg.currency == 1) {
            // USDT refund
            throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
            self.total_raised_usdt -= msg.amount;

            let usdt_config: USDTConfig = self.usdt_config!!;
            throwUnless(ERROR_USDT_NOT_CONFIGURED, usdt_config.wallet_address != null);

            // Send USDT refund via jetton transfer
            send(SendParameters{
                to: usdt_config.wallet_address!!,
                value: ton("0.1"), // Gas for jetton transfer
                mode: SendIgnoreErrors,
                bounce: false,
                body: JettonTransfer{
                    query_id: 0,
                    amount: refund_amount,
                    destination: msg.user,
                    response_destination: myAddress(),
                    custom_payload: null,
                    forward_ton_amount: 1, // 1 unit for notification
                    forward_payload: "USDT refund processed".asComment()
                }.toCell()
            });
        }
    }
    
    // End auction
    receive("end_auction") {
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, now() > self.auction_config.end_time || self.total_raised >= self.auction_config.hard_cap);
        
        if (self.total_raised >= self.auction_config.soft_cap) {
            self.auction_status = 2; // ended_success
        } else {
            self.auction_status = 3; // ended_failure
        }
    }
    
    // Initialize first round
    fun initializeFirstRound() {
        let first_round: RoundStats = RoundStats{
            round_number: 1,
            start_time: 0, // Will be set when auction starts
            end_time: 0,   // Will be set when auction starts
            price: self.current_price,
            total_raised_ton: 0,
            total_raised_usdt: 0,
            tokens_sold: 0,
            purchase_count: 0,
            unique_users: 0,
            refund_count: 0,
            refunded_amount_ton: 0,
            refunded_amount_usdt: 0
        };
        self.round_stats.set(1, first_round);
    }

    // Update current round based on time
    fun updateCurrentRound(current_time: Int) {
        let elapsed_time: Int = current_time - self.auction_config.start_time;
        let new_round: Int = (elapsed_time / self.ROUND_DURATION) + 1;

        if (new_round > self.current_round) {
            // Finalize current round
            self.finalizeCurrentRound(current_time);

            // Start new round
            self.current_round = new_round;
            self.current_price += self.PRICE_INCREMENT * (new_round - self.current_round);

            // Initialize new round stats
            self.initializeNewRound(new_round, current_time);
        }
    }

    // Finalize current round statistics
    fun finalizeCurrentRound(current_time: Int) {
        let current_stats: RoundStats? = self.round_stats.get(self.current_round);
        if (current_stats != null) {
            let stats: RoundStats = current_stats!!;
            let updated_stats: RoundStats = RoundStats{
                round_number: stats.round_number,
                start_time: stats.start_time,
                end_time: current_time,
                price: stats.price,
                total_raised_ton: stats.total_raised_ton,
                total_raised_usdt: stats.total_raised_usdt,
                tokens_sold: stats.tokens_sold,
                purchase_count: stats.purchase_count,
                unique_users: stats.unique_users,
                refund_count: stats.refund_count,
                refunded_amount_ton: stats.refunded_amount_ton,
                refunded_amount_usdt: stats.refunded_amount_usdt
            };
            self.round_stats.set(self.current_round, updated_stats);
        }
    }

    // Initialize new round
    fun initializeNewRound(round_number: Int, start_time: Int) {
        let new_round: RoundStats = RoundStats{
            round_number: round_number,
            start_time: start_time,
            end_time: 0,
            price: self.current_price,
            total_raised_ton: 0,
            total_raised_usdt: 0,
            tokens_sold: 0,
            purchase_count: 0,
            unique_users: 0,
            refund_count: 0,
            refunded_amount_ton: 0,
            refunded_amount_usdt: 0
        };
        self.round_stats.set(round_number, new_round);
    }

    // Update round statistics for a purchase
    fun updateRoundStatsForPurchase(user: Address, amount: Int, tokens: Int, currency: Int) {
        let current_stats: RoundStats? = self.round_stats.get(self.current_round);
        if (current_stats != null) {
            let stats: RoundStats = current_stats!!;

            // We'll determine if this is a new user by checking the user's purchase contract
            // This is now handled in the createOrUpdateUserPurchase function
            // For now, we'll increment unique_users and let the UserPurchase contract handle deduplication

            // Update round statistics
            let updated_stats: RoundStats = RoundStats{
                round_number: stats.round_number,
                start_time: stats.start_time,
                end_time: stats.end_time,
                price: stats.price,
                total_raised_ton: stats.total_raised_ton + (currency == 0 ? amount : 0),
                total_raised_usdt: stats.total_raised_usdt + (currency == 1 ? amount : 0),
                tokens_sold: stats.tokens_sold + tokens,
                purchase_count: stats.purchase_count + 1,
                unique_users: stats.unique_users + 1, // We'll let UserPurchase handle deduplication
                refund_count: stats.refund_count,
                refunded_amount_ton: stats.refunded_amount_ton,
                refunded_amount_usdt: stats.refunded_amount_usdt
            };
            self.round_stats.set(self.current_round, updated_stats);
        }
    }

    // Update round statistics for a refund
    fun updateRoundStatsForRefund(round_number: Int, amount: Int, currency: Int) {
        let current_stats: RoundStats? = self.round_stats.get(round_number);
        if (current_stats != null) {
            let stats: RoundStats = current_stats!!;
            let updated_stats: RoundStats = RoundStats{
                round_number: stats.round_number,
                start_time: stats.start_time,
                end_time: stats.end_time,
                price: stats.price,
                total_raised_ton: stats.total_raised_ton,
                total_raised_usdt: stats.total_raised_usdt,
                tokens_sold: stats.tokens_sold,
                purchase_count: stats.purchase_count,
                unique_users: stats.unique_users,
                refund_count: stats.refund_count + 1,
                refunded_amount_ton: stats.refunded_amount_ton + (currency == 0 ? amount : 0),
                refunded_amount_usdt: stats.refunded_amount_usdt + (currency == 1 ? amount : 0)
            };
            self.round_stats.set(round_number, updated_stats);
        }
    }
    
    // Create or update user purchase contract
    fun createOrUpdateUserPurchase(user: Address, amount: Int, tokens: Int, currency: Int, purchase_method: Int, nonce: Int) {
        // Calculate user purchase contract address deterministically
        let init_code: StateInit = self.getUserPurchaseInit(user);
        let user_purchase_addr: Address = contractAddress(init_code);

        // Always send message to the calculated address
        // If contract doesn't exist, it will be deployed automatically
        send(SendParameters{
            to: user_purchase_addr,
            value: ton("0.2"), // Gas for contract deployment/operation
            mode: SendIgnoreErrors,
            bounce: false,
            body: CreateUserPurchase{
                user: user,
                amount: amount,
                tokens: tokens,
                currency: currency,
                purchase_method: purchase_method,
                nonce: nonce,
                round_number: self.current_round
            }.toCell(),
            code: init_code.code,
            data: init_code.data
        });

        // Note: The UserPurchase contract will now track which rounds the user has participated in
        // We don't need to track this in the main contract anymore
    }

    // Handle USDT purchase with signature verification
    fun handleUSDTWithSignature(msg: JettonTransferNotification) {
        throwUnless(ERROR_SIGNING_KEY_NOT_SET, self.signing_public_key != 0);

        // Parse signature verification data from forward_payload
        let parsed_data: ParsedPurchaseData = self.parsePurchaseFromPayload(msg.forward_payload!!);
        let calc: PurchaseCalculation = parsed_data.calculation;
        let signature: Slice = parsed_data.signature;

        // Verify calculation data matches the transfer
        throwUnless(ERROR_CURRENCY_MISMATCH, calc.currency == 1);
        throwUnless(ERROR_AMOUNT_MISMATCH, calc.amount == msg.amount);
        throwUnless(ERROR_USER_MISMATCH, calc.user == msg.sender);

        // Verify signature timestamp and nonce
        let current_time: Int = now();
        throwUnless(ERROR_SIGNATURE_EXPIRED, current_time - calc.timestamp <= self.signature_timeout);
        throwUnless(ERROR_FUTURE_TIMESTAMP, calc.timestamp <= current_time);
        throwUnless(ERROR_NONCE_ALREADY_USED, self.used_nonces.get(calc.nonce) == null);

        // Verify the signature
        let data_hash: Int = self.hashPurchaseCalculation(calc);
        throwUnless(ERROR_INVALID_SIGNATURE, checkSignature(data_hash, signature, self.signing_public_key));

        // Mark nonce as used
        self.used_nonces.set(calc.nonce, true);

        // Use pre-calculated values from signature
        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + calc.tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised_usdt += calc.amount;
        self.total_tokens_sold += calc.tokens_to_receive;
        self.purchase_count += 1;

        // Update round statistics
        self.updateRoundStatsForPurchase(calc.user, calc.amount, calc.tokens_to_receive, 1);

        // Check if hard cap reached
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create user purchase with signature verification
        self.createOrUpdateUserPurchase(calc.user, calc.amount, calc.tokens_to_receive, 1, 1, calc.nonce);
    }

    // Handle direct USDT purchase (backward compatibility)
    fun handleDirectUSDTPurchase(msg: JettonTransferNotification) {
        // Convert USDT amount to TON equivalent for token calculation
        // USDT has 6 decimals, TON has 9 decimals
        let usdt_amount_in_ton_units: Int = msg.amount * 1000; // Convert 6 decimals to 9 decimals
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, usdt_amount_in_ton_units >= self.min_purchase);

        let current_time: Int = now();
        self.updateCurrentRound(current_time);

        // Calculate tokens to receive (using same price as TON)
        let tokens_to_receive: Int = (usdt_amount_in_ton_units * ton("1")) / self.current_price;

        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised_usdt += msg.amount; // Store in USDT units
        self.total_tokens_sold += tokens_to_receive;
        self.purchase_count += 1;

        // Update round statistics
        self.updateRoundStatsForPurchase(msg.sender, msg.amount, tokens_to_receive, 1);

        // Check if hard cap reached (convert USDT to TON for comparison)
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(msg.sender, msg.amount, tokens_to_receive, 1, 0, 0); // Direct USDT purchase, no nonce
    }

    // Parse purchase calculation and signature from forward_payload
    fun parsePurchaseFromPayload(payload: Cell): ParsedPurchaseData {
        let payload_slice: Slice = payload.beginParse();

        // Parse PurchaseCalculation
        let calc: PurchaseCalculation = PurchaseCalculation{
            user: payload_slice.loadAddress(),
            amount: payload_slice.loadCoins(),
            currency: payload_slice.loadUint(8),
            tokens_to_receive: payload_slice.loadCoins(),
            current_price: payload_slice.loadCoins(),
            current_round: payload_slice.loadUint(32),
            timestamp: payload_slice.loadUint(64),
            nonce: payload_slice.loadUint(64)
        };

        // Parse signature from reference
        let signature: Slice = payload_slice.loadRef().beginParse();

        return ParsedPurchaseData{
            calculation: calc,
            signature: signature
        };
    }

    // Hash purchase calculation for signature verification
    fun hashPurchaseCalculation(calc: PurchaseCalculation): Int {
        // Create a cell with all calculation data
        let data_cell: Cell = beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .endCell();

        // Return the hash of the cell
        return data_cell.hash();
    }

    // Get user purchase contract init
    fun getUserPurchaseInit(user: Address): StateInit {
        return initOf UserPurchase(myAddress(), user);
    }

    // Get jetton wallet address for a given owner and jetton master
    // Note: This is a simplified calculation. In practice, you should call the jetton master's get_wallet_address method
    fun getJettonWalletAddress(jetton_master: Address, owner: Address): Address {
        // For now, we'll store the expected wallet address in the USDT config
        // This should be set when configuring USDT
        if (self.usdt_config != null) {
            let config: USDTConfig = self.usdt_config!!;
            if (config.wallet_address != null) {
                return config.wallet_address!!;
            }
        }

        // Fallback: return the jetton master address (this is not correct but prevents compilation errors)
        // In production, this should be properly implemented
        return jetton_master;
    }


    
    // Getters
    get fun auction_info(): AuctionConfig {
        return self.auction_config;
    }
    
    get fun current_round(): Int {
        return self.current_round;
    }
    
    get fun current_price(): Int {
        return self.current_price;
    }
    
    get fun total_raised(): Int {
        return self.total_raised;
    }
    
    get fun total_tokens_sold(): Int {
        return self.total_tokens_sold;
    }
    
    get fun auction_status(): Int {
        return self.auction_status;
    }
    
    get fun purchase_count(): Int {
        return self.purchase_count;
    }
    
    get fun user_purchase_address(user: Address): Address {
        return contractAddress(self.getUserPurchaseInit(user));
    }
    
    get fun remaining_tokens(): Int {
        return self.auction_config.total_supply - self.total_tokens_sold;
    }
    
    get fun is_auction_active(): Bool {
        let current_time: Int = now();
        return self.auction_status == 1 &&
               current_time >= self.auction_config.start_time &&
               current_time <= self.auction_config.end_time;
    }

    get fun usdt_config(): USDTConfig? {
        return self.usdt_config;
    }

    get fun total_raised_usdt(): Int {
        return self.total_raised_usdt;
    }

    get fun total_raised_equivalent(): Int {
        // Return total raised in TON equivalent
        return self.total_raised + (self.total_raised_usdt * 1000);
    }

    get fun is_usdt_enabled(): Bool {
        return self.usdt_config != null;
    }

    // New getters for signature verification
    get fun signing_public_key(): Int {
        return self.signing_public_key;
    }

    get fun signature_timeout(): Int {
        return self.signature_timeout;
    }

    get fun is_nonce_used(nonce: Int): Bool {
        return self.used_nonces.get(nonce) != null;
    }

    get fun is_signature_verification_enabled(): Bool {
        return self.signing_public_key != 0;
    }

    get fun min_purchase(): Int {
        return self.min_purchase;
    }

    // Treasury and withdrawal getters
    get fun treasury_address(): Address? {
        return self.treasury_address;
    }

    get fun withdrawable_ton(): Int {
        if (self.auction_status == 2) { // Successful auction
            return self.total_raised;
        }
        return 0;
    }

    get fun withdrawable_usdt(): Int {
        if (self.auction_status == 2) { // Successful auction
            return self.total_raised_usdt;
        }
        return 0;
    }

    get fun can_withdraw(): Bool {
        return self.auction_status == 2; // Only after successful auction
    }

    // Get withdrawal summary (using numeric keys)
    // Key 1: auction_status, Key 2: withdrawable_ton, Key 3: withdrawable_usdt
    get fun withdrawal_summary(): map<Int, Int> {
        let summary: map<Int, Int> = emptyMap();
        summary.set(1, self.auction_status); // 1 = auction status
        summary.set(2, self.withdrawable_ton()); // 2 = withdrawable TON
        summary.set(3, self.withdrawable_usdt()); // 3 = withdrawable USDT
        return summary;
    }

    // Round statistics getters
    get fun round_stats(round_number: Int): RoundStats? {
        return self.round_stats.get(round_number);
    }

    get fun current_round_stats(): RoundStats? {
        return self.round_stats.get(self.current_round);
    }

    get fun total_rounds(): Int {
        let current_time: Int = now();
        if (self.auction_status != 1 || current_time < self.auction_config.start_time) {
            return 0;
        }
        let elapsed_time: Int = current_time - self.auction_config.start_time;
        return (elapsed_time / self.ROUND_DURATION) + 1;
    }

    // Note: User participation tracking is now handled by UserPurchase contracts
    // To get user participation data, query the user's UserPurchase contract directly using user_purchase_address(user)

    get fun round_summary(round_number: Int): RoundStats? {
        let stats: RoundStats? = self.round_stats.get(round_number);
        if (stats == null) {
            return null;
        }

        // If this is the current round and it's still active, update end_time to current time
        if (round_number == self.current_round && self.auction_status == 1) {
            let current_stats: RoundStats = stats!!;
            let current_time: Int = now();
            return RoundStats{
                round_number: current_stats.round_number,
                start_time: current_stats.start_time,
                end_time: current_time, // Current time for active round
                price: current_stats.price,
                total_raised_ton: current_stats.total_raised_ton,
                total_raised_usdt: current_stats.total_raised_usdt,
                tokens_sold: current_stats.tokens_sold,
                purchase_count: current_stats.purchase_count,
                unique_users: current_stats.unique_users,
                refund_count: current_stats.refund_count,
                refunded_amount_ton: current_stats.refunded_amount_ton,
                refunded_amount_usdt: current_stats.refunded_amount_usdt
            };
        }

        return stats;
    }

    // Get all rounds summary (for frontend display)
    get fun all_rounds_summary(): map<Int, RoundStats> {
        let summary: map<Int, RoundStats> = emptyMap();
        let max_rounds: Int = self.total_rounds();
        let i: Int = 1;

        while (i <= max_rounds) {
            let round_stats: RoundStats? = self.round_summary(i);
            if (round_stats != null) {
                summary.set(i, round_stats!!);
            }
            i += 1;
        }

        return summary;
    }

    // Get aggregated statistics across all rounds
    // Get round participants count from round statistics
    get fun round_participants_count(round_number: Int): Int {
        let round_stats: RoundStats? = self.round_stats.get(round_number);
        if (round_stats != null) {
            return round_stats!!.unique_users;
        }
        return 0;
    }

    get fun aggregated_stats(): RoundStats {
        let total_raised_ton: Int = 0;
        let total_raised_usdt: Int = 0;
        let total_tokens_sold: Int = 0;
        let total_purchases: Int = 0;
        let total_unique_users: Int = 0;
        let total_refunds: Int = 0;
        let total_refunded_ton: Int = 0;
        let total_refunded_usdt: Int = 0;

        let max_rounds: Int = self.total_rounds();
        let i: Int = 1;

        while (i <= max_rounds) {
            let round_stats: RoundStats? = self.round_stats.get(i);
            if (round_stats != null) {
                let stats: RoundStats = round_stats!!;
                total_raised_ton += stats.total_raised_ton;
                total_raised_usdt += stats.total_raised_usdt;
                total_tokens_sold += stats.tokens_sold;
                total_purchases += stats.purchase_count;
                total_refunds += stats.refund_count;
                total_refunded_ton += stats.refunded_amount_ton;
                total_refunded_usdt += stats.refunded_amount_usdt;
                total_unique_users += stats.unique_users;
            }
            i += 1;
        }

        // Note: This is a simplified approach that might count users multiple times
        // A more accurate count would require tracking unique users across all rounds

        return RoundStats{
            round_number: 0, // 0 indicates aggregated stats
            start_time: self.auction_config.start_time,
            end_time: now(),
            price: 0, // Not applicable for aggregated stats
            total_raised_ton: total_raised_ton,
            total_raised_usdt: total_raised_usdt,
            tokens_sold: total_tokens_sold,
            purchase_count: total_purchases,
            unique_users: total_unique_users, // Note: This might count users multiple times
            refund_count: total_refunds,
            refunded_amount_ton: total_refunded_ton,
            refunded_amount_usdt: total_refunded_usdt
        };
    }
}
