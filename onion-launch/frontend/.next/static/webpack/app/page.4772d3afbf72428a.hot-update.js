"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/contractUtils.ts":
/*!**********************************!*\
  !*** ./src/lib/contractUtils.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callContractGetMethod: () => (/* binding */ callContractGetMethod),\n/* harmony export */   getPurchaseCount: () => (/* binding */ getPurchaseCount),\n/* harmony export */   getPurchaseDetails: () => (/* binding */ getPurchaseDetails),\n/* harmony export */   getSignatureVerifiedPurchases: () => (/* binding */ getSignatureVerifiedPurchases),\n/* harmony export */   getTotalPaid: () => (/* binding */ getTotalPaid),\n/* harmony export */   getTotalPurchased: () => (/* binding */ getTotalPurchased),\n/* harmony export */   isRefunded: () => (/* binding */ isRefunded),\n/* harmony export */   parsePurchaseDetails: () => (/* binding */ parsePurchaseDetails)\n/* harmony export */ });\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Independent contract interaction utilities\n * \n * This module provides simple, dependency-free methods for calling\n * contract get methods and parsing results, avoiding complex wrapper dependencies.\n */ \n/**\n * Independent method to call contract get methods\n * Provides a simple interface without wrapper dependencies\n */ const callContractGetMethod = async (client, address, method, args)=>{\n    return await client.runMethod(address, method, args);\n};\n/**\n * Parse purchase details from contract response\n * Converts raw stack data to structured PurchaseRecord object\n */ const parsePurchaseDetails = (result, userPurchaseAddress)=>{\n    try {\n        if (result.stack.remaining === 0) {\n            return null;\n        }\n        // Read the PurchaseRecord struct from the stack\n        const source = result.stack.readTupleOpt();\n        const _id = source.pop();\n        const _user = source.pop();\n        const _amount = source.pop();\n        const _tokens = source.pop();\n        const _timestamp = source.pop();\n        const _currency = source.pop();\n        const _purchase_method = source.pop();\n        const _nonce = source.pop();\n        return {\n            id: Number_id,\n            amount: _amount,\n            tokens: _tokens,\n            timestamp: Number(_timestamp),\n            currency: Number(_currency),\n            purchase_method: Number(_purchase_method),\n            nonce: _nonce,\n            user: _user\n        };\n    } catch (error) {\n        console.error('Error parsing purchase details:', error);\n        return null;\n    }\n};\n/**\n * Get purchase count from UserPurchase contract\n */ const getPurchaseCount = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', []);\n    return result.stack.readNumber();\n};\n/**\n * Get purchase details by ID\n */ const getPurchaseDetails = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build());\n    return parsePurchaseDetails(result, userPurchaseAddress);\n};\n/**\n * Check if purchase is refunded\n */ const isRefunded = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build());\n    return result.stack.readBoolean();\n};\n/**\n * Get total purchased tokens\n */ const getTotalPurchased = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get total paid amount\n */ const getTotalPaid = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get signature verified purchases count\n */ const getSignatureVerifiedPurchases = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', []);\n    return result.stack.readNumber();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/contractUtils.ts\n"));

/***/ })

});