"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/contractUtils.ts":
/*!**********************************!*\
  !*** ./src/lib/contractUtils.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callContractGetMethod: () => (/* binding */ callContractGetMethod),\n/* harmony export */   getPurchaseCount: () => (/* binding */ getPurchaseCount),\n/* harmony export */   getPurchaseDetails: () => (/* binding */ getPurchaseDetails),\n/* harmony export */   getSignatureVerifiedPurchases: () => (/* binding */ getSignatureVerifiedPurchases),\n/* harmony export */   getTotalPaid: () => (/* binding */ getTotalPaid),\n/* harmony export */   getTotalPurchased: () => (/* binding */ getTotalPurchased),\n/* harmony export */   isRefunded: () => (/* binding */ isRefunded),\n/* harmony export */   parsePurchaseDetails: () => (/* binding */ parsePurchaseDetails)\n/* harmony export */ });\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Independent contract interaction utilities\n * \n * This module provides simple, dependency-free methods for calling\n * contract get methods and parsing results, avoiding complex wrapper dependencies.\n */ \n/**\n * Independent method to call contract get methods\n * Provides a simple interface without wrapper dependencies\n */ const callContractGetMethod = async (client, address, method, args)=>{\n    return await client.runMethod(address, method, args);\n};\n/**\n * Parse purchase details from contract response\n * Converts raw stack data to structured PurchaseRecord object\n */ const parsePurchaseDetails = (result)=>{\n    try {\n        if (result.stack.remaining === 0) {\n            return null;\n        }\n        // Read the PurchaseRecord struct from the stack\n        const record = result.stack.readTuple();\n        //console.log('Parsed purchase record:', record)\n        record.readBigNumber();\n        record.readBigNumber();\n        return {\n            id: record.readNumber(),\n            user: record.readAddress(),\n            amount: record.readBigNumber(),\n            tokens: record.readBigNumber(),\n            timestamp: record.readNumber(),\n            currency: record.readNumber(),\n            purchase_method: record.readNumber(),\n            nonce: record.readBigNumber()\n        };\n    } catch (error) {\n        console.error('Error parsing purchase details:', error);\n        return null;\n    }\n};\n/**\n * Get purchase count from UserPurchase contract\n */ const getPurchaseCount = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', []);\n    return result.stack.readNumber();\n};\n/**\n * Get purchase details by ID\n */ const getPurchaseDetails = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build());\n    return parsePurchaseDetails(result);\n};\n/**\n * Check if purchase is refunded\n */ const isRefunded = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build());\n    return result.stack.readBoolean();\n};\n/**\n * Get total purchased tokens\n */ const getTotalPurchased = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get total paid amount\n */ const getTotalPaid = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get signature verified purchases count\n */ const getSignatureVerifiedPurchases = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', []);\n    return result.stack.readNumber();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/contractUtils.ts\n"));

/***/ })

});