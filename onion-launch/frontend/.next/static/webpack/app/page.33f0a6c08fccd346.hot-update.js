"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* harmony import */ var _lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/usdtPurchase */ \"(app-pages-browser)/./src/lib/usdtPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\n// Helper function to calculate jetton wallet address\nasync function getJettonWalletAddress(ownerAddress, jettonMasterAddress) {\n    try {\n        // Use TonWeb to calculate jetton wallet address\n        const TonWeb = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_tonweb_src_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! tonweb */ \"(app-pages-browser)/./node_modules/tonweb/src/index.js\", 23))).default;\n        const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'));\n        //@ts-ignore\n        const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {\n            address: jettonMasterAddress\n        });\n        const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(new TonWeb.utils.Address(ownerAddress));\n        return jettonWalletAddress.toString();\n    } catch (error) {\n        console.error('Failed to get jetton wallet address:', error);\n        throw new Error('Failed to calculate USDT wallet address');\n    }\n}\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Handle different currencies\n                if (calc.currency.toString() === '0') {\n                    // TON purchase - direct contract call\n                    return await executeTONPurchase(calc, signature);\n                } else if (calc.currency.toString() === '1') {\n                    // USDT purchase - jetton transfer\n                    return await executeUSDTPurchase(calc, signature);\n                } else {\n                    throw new Error('Unsupported currency');\n                }\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Execute TON purchase with signature verification\n   */ const executeTONPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeTONPurchase]\": async (calc, signature)=>{\n            // Convert signature from base64 to buffer\n            const signatureBuffer = Buffer.from(signature, 'base64');\n            // Create signature cell\n            const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n            // Create the PurchaseWithSignature message body following the exact generated structure\n            const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n            // Store PurchaseCalculation inline (not as a separate cell)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n            .storeRef(signatureCell).endCell();\n            console.log('Message structure:', {\n                opCode: '0x' + 1524770820..toString(16),\n                signatureCellBits: signatureCell.bits.length,\n                totalBodyBits: purchaseBody.bits.length,\n                bodyBocLength: purchaseBody.toBoc().length\n            });\n            // Create transaction request\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: CONTRACT_ADDRESS,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                        payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('Transaction request:', {\n                contractAddress: CONTRACT_ADDRESS,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('TON Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeTONPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"], [\n        tonConnectUI\n    ]);\n    /**\n   * Execute USDT purchase with signature verification\n   */ const executeUSDTPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": async (calc, signature)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                throw new Error('Wallet not connected');\n            }\n            // Get user's USDT jetton wallet address\n            const userUSDTWalletAddress = await getJettonWalletAddress(wallet.account.address, USDT_JETTON_MASTER_ADDRESS);\n            console.log('User USDT wallet address:', userUSDTWalletAddress);\n            // Build forward payload with signature verification data\n            const purchaseCalc = {\n                user: _ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user),\n                amount: BigInt(calc.amount),\n                currency: BigInt(calc.currency),\n                tokensToReceive: BigInt(calc.tokens_to_receive),\n                currentPrice: BigInt(calc.current_price),\n                currentRound: calc.current_round,\n                timestamp: calc.timestamp,\n                nonce: BigInt(calc.nonce)\n            };\n            const forwardPayload = (0,_lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__.buildUSDTForwardPayload)(purchaseCalc, signature);\n            // Create jetton transfer message\n            const jettonTransferBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0xf8a7ea5, 32) // JettonTransfer op code\n            .storeUint(0, 64) // query_id\n            .storeCoins(BigInt(calc.amount)) // amount\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(CONTRACT_ADDRESS)) // destination (auction contract)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(wallet.account.address)) // response_destination\n            .storeMaybeRef(null) // custom_payload\n            .storeCoins((0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.05')) // forward_ton_amount (gas for signature verification)\n            .storeMaybeRef(forwardPayload) // forward_payload with signature data\n            .endCell();\n            console.log('USDT Transfer details:', {\n                userWallet: userUSDTWalletAddress,\n                amount: calc.amount,\n                destination: CONTRACT_ADDRESS,\n                forwardPayloadSize: forwardPayload.toBoc().length\n            });\n            // Create transaction request for USDT transfer\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: userUSDTWalletAddress,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                        payload: Buffer.from(jettonTransferBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('USDT Transaction request:', {\n                jettonWalletAddress: userUSDTWalletAddress,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('USDT Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"], [\n        wallet,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});