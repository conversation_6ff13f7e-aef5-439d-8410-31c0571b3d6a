"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/contractUtils.ts":
/*!**********************************!*\
  !*** ./src/lib/contractUtils.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callContractGetMethod: () => (/* binding */ callContractGetMethod),\n/* harmony export */   getPurchaseCount: () => (/* binding */ getPurchaseCount),\n/* harmony export */   getPurchaseDetails: () => (/* binding */ getPurchaseDetails),\n/* harmony export */   getSignatureVerifiedPurchases: () => (/* binding */ getSignatureVerifiedPurchases),\n/* harmony export */   getTotalPaid: () => (/* binding */ getTotalPaid),\n/* harmony export */   getTotalPurchased: () => (/* binding */ getTotalPurchased),\n/* harmony export */   isRefunded: () => (/* binding */ isRefunded),\n/* harmony export */   parsePurchaseDetails: () => (/* binding */ parsePurchaseDetails)\n/* harmony export */ });\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Independent contract interaction utilities\n * \n * This module provides simple, dependency-free methods for calling\n * contract get methods and parsing results, avoiding complex wrapper dependencies.\n */ \n/**\n * Independent method to call contract get methods\n * Provides a simple interface without wrapper dependencies\n */ const callContractGetMethod = async (client, address, method, args)=>{\n    return await client.runMethod(address, method, args);\n};\n/**\n * Parse purchase details from contract response\n * Converts raw stack data to structured PurchaseRecord object\n */ const parsePurchaseDetails = (result, userPurchaseAddress)=>{\n    try {\n        if (result.stack.remaining === 0) {\n            return null;\n        }\n        // Read the PurchaseRecord struct from the stack\n        const record = result.stack.readTuple();\n        record.readCell();\n        return {\n            id: record.readBigNumber(),\n            amount: record.readBigNumber(),\n            tokens: record.readBigNumber(),\n            timestamp: record.readBigNumber(),\n            currency: record.readBigNumber(),\n            purchase_method: record.readBigNumber(),\n            nonce: record.readBigNumber(),\n            user: userPurchaseAddress\n        };\n    } catch (error) {\n        console.error('Error parsing purchase details:', error);\n        return null;\n    }\n};\n/**\n * Get purchase count from UserPurchase contract\n */ const getPurchaseCount = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', []);\n    return result.stack.readNumber();\n};\n/**\n * Get purchase details by ID\n */ const getPurchaseDetails = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build());\n    return parsePurchaseDetails(result, userPurchaseAddress);\n};\n/**\n * Check if purchase is refunded\n */ const isRefunded = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build());\n    return result.stack.readBoolean();\n};\n/**\n * Get total purchased tokens\n */ const getTotalPurchased = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get total paid amount\n */ const getTotalPaid = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get signature verified purchases count\n */ const getSignatureVerifiedPurchases = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', []);\n    return result.stack.readNumber();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/contractUtils.ts\n"));

/***/ })

});