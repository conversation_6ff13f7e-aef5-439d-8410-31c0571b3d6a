"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/contractUtils.ts":
/*!**********************************!*\
  !*** ./src/lib/contractUtils.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callContractGetMethod: () => (/* binding */ callContractGetMethod),\n/* harmony export */   getPurchaseCount: () => (/* binding */ getPurchaseCount),\n/* harmony export */   getPurchaseDetails: () => (/* binding */ getPurchaseDetails),\n/* harmony export */   getSignatureVerifiedPurchases: () => (/* binding */ getSignatureVerifiedPurchases),\n/* harmony export */   getTotalPaid: () => (/* binding */ getTotalPaid),\n/* harmony export */   getTotalPurchased: () => (/* binding */ getTotalPurchased),\n/* harmony export */   isRefunded: () => (/* binding */ isRefunded),\n/* harmony export */   parsePurchaseDetails: () => (/* binding */ parsePurchaseDetails)\n/* harmony export */ });\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Independent contract interaction utilities\n * \n * This module provides simple, dependency-free methods for calling\n * contract get methods and parsing results, avoiding complex wrapper dependencies.\n */ \n/**\n * Independent method to call contract get methods\n * Provides a simple interface without wrapper dependencies\n */ const callContractGetMethod = async (client, address, method, args)=>{\n    return await client.runMethod(address, method, args);\n};\n/**\n * Parse purchase details from contract response\n * Converts raw stack data to structured PurchaseRecord object\n */ const parsePurchaseDetails = (result, userPurchaseAddress)=>{\n    try {\n        if (result.stack.remaining === 0) {\n            return null;\n        }\n        // Read the PurchaseRecord struct from the stack\n        const source = result.stack;\n        const _id = source.readBigNumber();\n        const _user = source.readAddress();\n        const _amount = source.readBigNumber();\n        const _tokens = source.readBigNumber();\n        const _timestamp = source.readBigNumber();\n        const _currency = source.readBigNumber();\n        const _purchase_method = source.readBigNumber();\n        const _nonce = source.readBigNumber();\n        return {\n            id: _id,\n            amount: _amount,\n            tokens: _tokens,\n            timestamp: _timestamp,\n            currency: _currency,\n            purchase_method: _purchase_method,\n            nonce: _nonce,\n            user: _user\n        };\n    } catch (error) {\n        console.error('Error parsing purchase details:', error);\n        return null;\n    }\n};\n/**\n * Get purchase count from UserPurchase contract\n */ const getPurchaseCount = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', []);\n    return result.stack.readNumber();\n};\n/**\n * Get purchase details by ID\n */ const getPurchaseDetails = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build());\n    return parsePurchaseDetails(result, userPurchaseAddress);\n};\n/**\n * Check if purchase is refunded\n */ const isRefunded = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build());\n    return result.stack.readBoolean();\n};\n/**\n * Get total purchased tokens\n */ const getTotalPurchased = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get total paid amount\n */ const getTotalPaid = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get signature verified purchases count\n */ const getSignatureVerifiedPurchases = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', []);\n    return result.stack.readNumber();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/contractUtils.ts\n"));

/***/ })

});