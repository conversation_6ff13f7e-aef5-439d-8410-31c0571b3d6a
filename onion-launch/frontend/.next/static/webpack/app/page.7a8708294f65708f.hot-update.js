"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* harmony import */ var _lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/usdtPurchase */ \"(app-pages-browser)/./src/lib/usdtPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\n// Helper function to calculate jetton wallet address\nasync function getJettonWalletAddress(ownerAddress, jettonMasterAddress) {\n    try {\n        // Use TonWeb to calculate jetton wallet address\n        const TonWeb = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_tonweb_src_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! tonweb */ \"(app-pages-browser)/./node_modules/tonweb/src/index.js\", 23))).default;\n        const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'));\n        //@ts-ignore\n        const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {\n            address: jettonMasterAddress\n        });\n        const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(new TonWeb.utils.Address(ownerAddress));\n        return jettonWalletAddress.toString();\n    } catch (error) {\n        console.error('Failed to get jetton wallet address:', error);\n        throw new Error('Failed to calculate USDT wallet address');\n    }\n}\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Handle different currencies\n                if (calc.currency.toString() === '0') {\n                    // TON purchase - direct contract call\n                    return await executeTONPurchase(calc, signature);\n                } else if (calc.currency.toString() === '1') {\n                    // USDT purchase - jetton transfer\n                    return await executeUSDTPurchase(calc, signature);\n                } else {\n                    throw new Error('Unsupported currency');\n                }\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Execute TON purchase with signature verification\n   */ const executeTONPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeTONPurchase]\": async (calc, signature)=>{\n            // Convert signature from base64 to buffer\n            const signatureBuffer = Buffer.from(signature, 'base64');\n            // Create signature cell\n            const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n            // Create the PurchaseWithSignature message body following the exact generated structure\n            const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n            // Store PurchaseCalculation inline (not as a separate cell)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n            .storeRef(signatureCell).endCell();\n            console.log('Message structure:', {\n                opCode: '0x' + 1524770820..toString(16),\n                signatureCellBits: signatureCell.bits.length,\n                totalBodyBits: purchaseBody.bits.length,\n                bodyBocLength: purchaseBody.toBoc().length\n            });\n            // Create transaction request\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: CONTRACT_ADDRESS,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                        payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('Transaction request:', {\n                contractAddress: CONTRACT_ADDRESS,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('TON Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeTONPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"], [\n        tonConnectUI\n    ]);\n    /**\n   * Execute USDT purchase with signature verification\n   */ const executeUSDTPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": async (calc, signature)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                throw new Error('Wallet not connected');\n            }\n            // Get user's USDT jetton wallet address\n            const userUSDTWalletAddress = await getJettonWalletAddress(wallet.account.address, USDT_JETTON_MASTER_ADDRESS);\n            console.log('User USDT wallet address:', userUSDTWalletAddress);\n            // Build forward payload with signature verification data\n            const purchaseCalc = {\n                user: _ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user),\n                amount: BigInt(calc.amount),\n                currency: Number(calc.currency),\n                tokensToReceive: BigInt(calc.tokens_to_receive),\n                currentPrice: BigInt(calc.current_price),\n                currentRound: calc.current_round,\n                timestamp: calc.timestamp,\n                nonce: BigInt(calc.nonce)\n            };\n            const forwardPayload = (0,_lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__.buildUSDTForwardPayload)(purchaseCalc, signature);\n            // Create jetton transfer message\n            const jettonTransferBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0xf8a7ea5, 32) // JettonTransfer op code\n            .storeUint(0, 64) // query_id\n            .storeCoins(BigInt(calc.amount)) // amount\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(CONTRACT_ADDRESS)) // destination (auction contract)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(wallet.account.address)) // response_destination\n            .storeMaybeRef(null) // custom_payload\n            .storeCoins((0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.05')) // forward_ton_amount (gas for signature verification)\n            .storeMaybeRef(forwardPayload) // forward_payload with signature data\n            .endCell();\n            console.log('USDT Transfer details:', {\n                userWallet: userUSDTWalletAddress,\n                amount: calc.amount,\n                destination: CONTRACT_ADDRESS,\n                forwardPayloadSize: forwardPayload.toBoc().length\n            });\n            // Create transaction request for USDT transfer\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: userUSDTWalletAddress,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                        payload: Buffer.from(jettonTransferBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('USDT Transaction request:', {\n                jettonWalletAddress: userUSDTWalletAddress,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('USDT Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"], [\n        wallet,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});