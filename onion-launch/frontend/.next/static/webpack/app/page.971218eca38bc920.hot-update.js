"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/contractUtils.ts":
/*!**********************************!*\
  !*** ./src/lib/contractUtils.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callContractGetMethod: () => (/* binding */ callContractGetMethod),\n/* harmony export */   getPurchaseCount: () => (/* binding */ getPurchaseCount),\n/* harmony export */   getPurchaseDetails: () => (/* binding */ getPurchaseDetails),\n/* harmony export */   getSignatureVerifiedPurchases: () => (/* binding */ getSignatureVerifiedPurchases),\n/* harmony export */   getTotalPaid: () => (/* binding */ getTotalPaid),\n/* harmony export */   getTotalPurchased: () => (/* binding */ getTotalPurchased),\n/* harmony export */   isRefunded: () => (/* binding */ isRefunded),\n/* harmony export */   parsePurchaseDetails: () => (/* binding */ parsePurchaseDetails)\n/* harmony export */ });\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Independent contract interaction utilities\n * \n * This module provides simple, dependency-free methods for calling\n * contract get methods and parsing results, avoiding complex wrapper dependencies.\n */ \n/**\n * Independent method to call contract get methods\n * Provides a simple interface without wrapper dependencies\n */ const callContractGetMethod = async (client, address, method, args)=>{\n    return await client.runMethod(address, method, args);\n};\n/**\n * Parse purchase details from contract response\n * Converts raw stack data to structured PurchaseRecord object\n */ const parsePurchaseDetails = (result, userPurchaseAddress)=>{\n    try {\n        if (result.stack.remaining === 0) {\n            return null;\n        }\n        // Read the PurchaseRecord struct from the stack\n        const source = result.stack;\n        const _id = source.readBigNumber();\n        const _user = source.readAddress();\n        const _amount = source.readBigNumber();\n        const _tokens = source.readBigNumber();\n        const _timestamp = source.readBigNumber();\n        const _currency = source.readBigNumber();\n        const _purchase_method = source.readBigNumber();\n        const _nonce = source.readBigNumber();\n        return {\n            id: _id,\n            amount: _amount,\n            tokens: _tokens,\n            timestamp: _timestamp,\n            currency: _currency,\n            purchase_method: _purchase_method,\n            nonce: _nonce,\n            user: userPurchaseAddress\n        };\n    } catch (error) {\n        console.error('Error parsing purchase details:', error);\n        return null;\n    }\n};\n/**\n * Get purchase count from UserPurchase contract\n */ const getPurchaseCount = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', []);\n    return result.stack.readNumber();\n};\n/**\n * Get purchase details by ID\n */ const getPurchaseDetails = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build());\n    return parsePurchaseDetails(result, userPurchaseAddress);\n};\n/**\n * Check if purchase is refunded\n */ const isRefunded = async (client, userPurchaseAddress, purchaseId)=>{\n    const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n    args.writeNumber(purchaseId);\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build());\n    return result.stack.readBoolean();\n};\n/**\n * Get total purchased tokens\n */ const getTotalPurchased = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get total paid amount\n */ const getTotalPaid = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', []);\n    return result.stack.readBigNumber();\n};\n/**\n * Get signature verified purchases count\n */ const getSignatureVerifiedPurchases = async (client, userPurchaseAddress)=>{\n    const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', []);\n    return result.stack.readNumber();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/contractUtils.ts\n"));

/***/ })

});