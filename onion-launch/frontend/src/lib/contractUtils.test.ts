/**
 * Test file for independent contract utilities
 * 
 * This demonstrates how to use the independent contract methods
 * without complex wrapper dependencies.
 */

import { Address, TonClient } from '@ton/ton'
import { 
  callContractGetMethod,
  parsePurchaseDetails,
  getPurchaseCount,
  getPurchaseDetails,
  isRefunded,
  getTotalPurchased,
  getTotalPaid,
  getSignatureVerifiedPurchases
} from './contractUtils'

// Test configuration
const TEST_AUCTION_ADDRESS = process.env.NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS || 'EQC...'
const TEST_USER_ADDRESS = 'EQxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' // Replace with test address

// TON client for testing
const getTestClient = () => {
  return new TonClient({
    endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',
    apiKey: process.env.NEXT_PUBLIC_TON_API_KEY
  })
}

/**
 * Test function to demonstrate independent contract calls
 */
export async function testContractUtils() {
  console.log('🧪 Testing Independent Contract Utilities')
  console.log('=========================================')

  try {
    const client = getTestClient()
    
    // Calculate user purchase address (you would get this from auction contract)
    // For testing, we'll use a mock address
    const userPurchaseAddress = Address.parse(TEST_USER_ADDRESS)

    console.log('📍 User Purchase Address:', userPurchaseAddress.toString())

    // Test 1: Get purchase count
    console.log('\n1️⃣ Testing getPurchaseCount...')
    try {
      const purchaseCount = await getPurchaseCount(client, userPurchaseAddress)
      console.log('✅ Purchase count:', purchaseCount)
    } catch (error) {
      console.log('❌ Purchase count failed:', error)
    }

    // Test 2: Get purchase details
    console.log('\n2️⃣ Testing getPurchaseDetails...')
    try {
      const purchaseDetails = await getPurchaseDetails(client, userPurchaseAddress, 1)
      if (purchaseDetails) {
        console.log('✅ Purchase details for ID 1:')
        console.log('  - ID:', purchaseDetails.id)
        console.log('  - Amount:', purchaseDetails.amount.toString())
        console.log('  - Tokens:', purchaseDetails.tokens.toString())
        console.log('  - Currency:', purchaseDetails.currency === 0 ? 'TON' : 'USDT')
        console.log('  - Method:', purchaseDetails.purchase_method === 0 ? 'direct' : 'signature_verified')
        console.log('  - Timestamp:', new Date(purchaseDetails.timestamp * 1000).toISOString())
      } else {
        console.log('ℹ️ No purchase details found for ID 1')
      }
    } catch (error) {
      console.log('❌ Purchase details failed:', error)
    }

    // Test 3: Check refund status
    console.log('\n3️⃣ Testing isRefunded...')
    try {
      const refunded = await isRefunded(client, userPurchaseAddress, 1)
      console.log('✅ Purchase 1 refunded:', refunded)
    } catch (error) {
      console.log('❌ Refund check failed:', error)
    }

    // Test 4: Get totals
    console.log('\n4️⃣ Testing getTotalPurchased...')
    try {
      const totalPurchased = await getTotalPurchased(client, userPurchaseAddress)
      console.log('✅ Total purchased:', totalPurchased.toString())
    } catch (error) {
      console.log('❌ Total purchased failed:', error)
    }

    console.log('\n5️⃣ Testing getTotalPaid...')
    try {
      const totalPaid = await getTotalPaid(client, userPurchaseAddress)
      console.log('✅ Total paid:', totalPaid.toString())
    } catch (error) {
      console.log('❌ Total paid failed:', error)
    }

    // Test 5: Get signature verified purchases
    console.log('\n6️⃣ Testing getSignatureVerifiedPurchases...')
    try {
      const sigVerified = await getSignatureVerifiedPurchases(client, userPurchaseAddress)
      console.log('✅ Signature verified purchases:', sigVerified)
    } catch (error) {
      console.log('❌ Signature verified purchases failed:', error)
    }

    // Test 6: Direct contract call example
    console.log('\n7️⃣ Testing direct callContractGetMethod...')
    try {
      const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', [])
      const count = result.stack.readNumber()
      console.log('✅ Direct call result - purchase count:', count)
    } catch (error) {
      console.log('❌ Direct call failed:', error)
    }

    console.log('\n🎉 Contract utilities test completed!')

  } catch (error) {
    console.error('💥 Test failed:', error)
  }
}

/**
 * Example usage in a React component or script
 */
export function exampleUsage() {
  console.log(`
📖 Example Usage:

import { 
  getPurchaseCount, 
  getPurchaseDetails, 
  isRefunded 
} from './lib/contractUtils'

// In your component or function:
const client = new TonClient({ ... })
const userPurchaseAddress = Address.parse('...')

// Get purchase count
const count = await getPurchaseCount(client, userPurchaseAddress)

// Get specific purchase
const purchase = await getPurchaseDetails(client, userPurchaseAddress, 1)

// Check refund status
const refunded = await isRefunded(client, userPurchaseAddress, 1)

// These methods are independent and don't require contract wrappers!
  `)
}

// Run test if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  testContractUtils()
}
