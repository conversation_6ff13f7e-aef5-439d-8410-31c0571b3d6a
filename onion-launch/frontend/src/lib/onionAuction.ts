import { Address, beginCell, Cell, Contract, contractAddress, ContractProvider, Sender, SendMode } from '@ton/core'

export type OnionAuctionConfig = {
    owner: Address
    startTime: number
    endTime: number
    softCap: bigint
    hardCap: bigint
    totalSupply: bigint
}

export function onionAuctionConfigToCell(config: OnionAuctionConfig): Cell {
    return beginCell()
        .storeAddress(config.owner)
        .storeUint(config.startTime, 64)
        .storeUint(config.endTime, 64)
        .storeCoins(config.softCap)
        .storeCoins(config.hardCap)
        .storeCoins(config.totalSupply)
        .endCell()
}

export class OnionAuction implements Contract {
    constructor(readonly address: Address, readonly init?: { code: Cell; data: Cell }) {}

    static createFromAddress(address: Address) {
        return new OnionAuction(address)
    }

    static createFromConfig(config: OnionAuctionConfig, code: Cell, workchain = 0) {
        const data = onionAuctionConfigToCell(config)
        const init = { code, data }
        return new OnionAuction(contractAddress(workchain, init), init)
    }

    async sendDeploy(provider: ContractProvider, via: Sender, value: bigint) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell().endCell(),
        })
    }

    async sendPurchase(
        provider: ContractProvider,
        via: Sender,
        opts: {
            amount: bigint
            currency: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x1234, 32) // op code for Purchase
                .storeUint(0, 64) // query_id
                .storeCoins(opts.amount)
                .storeUint(opts.currency, 8)
                .endCell(),
        })
    }

    async sendRefund(
        provider: ContractProvider,
        via: Sender,
        opts: {
            purchaseId: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x5678, 32) // op code for Refund
                .storeUint(0, 64) // query_id
                .storeUint(opts.purchaseId, 32)
                .endCell(),
        })
    }

    async getAuctionInfo(provider: ContractProvider) {
        const result = await provider.get('auction_info', [])
        return {
            startTime: result.stack.readNumber(),
            endTime: result.stack.readNumber(),
            softCap: result.stack.readBigNumber(),
            hardCap: result.stack.readBigNumber(),
            totalSupply: result.stack.readBigNumber(),
            refundFeePercent: result.stack.readNumber(),
        }
    }

    async getCurrentRound(provider: ContractProvider) {
        const result = await provider.get('current_round', [])
        return result.stack.readNumber()
    }

    async getCurrentPrice(provider: ContractProvider) {
        const result = await provider.get('current_price', [])
        return result.stack.readBigNumber()
    }

    async getTotalRaised(provider: ContractProvider) {
        const result = await provider.get('total_raised', [])
        return result.stack.readBigNumber()
    }

    async getTotalTokensSold(provider: ContractProvider) {
        const result = await provider.get('total_tokens_sold', [])
        return result.stack.readBigNumber()
    }

    async getAuctionStatus(provider: ContractProvider) {
        const result = await provider.get('auction_status', [])
        return result.stack.readNumber()
    }

    async getRemainingTokens(provider: ContractProvider) {
        const result = await provider.get('remaining_tokens', [])
        return result.stack.readBigNumber()
    }

    async getUserPurchaseAddress(provider: ContractProvider, user: Address) {
        const result = await provider.get('user_purchase_address', [
            { type: 'slice', cell: beginCell().storeAddress(user).endCell() }
        ])
        return result.stack.readAddressOpt()
    }

    async isAuctionActive(provider: ContractProvider) {
        const result = await provider.get('is_auction_active', [])
        return result.stack.readBoolean()
    }

    // Treasury management methods
    async sendSetTreasury(provider: ContractProvider, via: Sender, value: bigint, treasuryAddress: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd3d3d3d3, 32) // SetTreasury op code
                .storeAddress(treasuryAddress)
                .endCell(),
        })
    }

    // Fund withdrawal methods
    async sendWithdrawTON(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd1d1d1d1, 32) // WithdrawTON op code
                .storeCoins(amount) // 0 = withdraw all
                .storeAddress(destination)
                .endCell(),
        })
    }

    async sendWithdrawUSDT(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd2d2d2d2, 32) // WithdrawUSDT op code
                .storeCoins(amount) // 0 = withdraw all
                .storeAddress(destination)
                .endCell(),
        })
    }

    // Withdrawal query methods
    async getTreasuryAddress(provider: ContractProvider) {
        const result = await provider.get('treasury_address', [])
        return result.stack.readAddressOpt()
    }

    async getWithdrawableTON(provider: ContractProvider) {
        const result = await provider.get('withdrawable_ton', [])
        return result.stack.readBigNumber()
    }

    async getWithdrawableUSDT(provider: ContractProvider) {
        const result = await provider.get('withdrawable_usdt', [])
        return result.stack.readBigNumber()
    }

    async canWithdraw(provider: ContractProvider) {
        const result = await provider.get('can_withdraw', [])
        return result.stack.readBoolean()
    }

    async getWithdrawalSummary(provider: ContractProvider) {
        const result = await provider.get('withdrawal_summary', [])
        const summary = new Map<number, bigint>()

        // Read the map from the stack
        // Note: This is a simplified version - actual implementation may vary
        try {
            while (result.stack.remaining > 0) {
                const key = result.stack.readNumber()
                const value = result.stack.readBigNumber()
                summary.set(key, value)
            }
        } catch (e) {
            // Handle end of stack
        }

        return {
            auctionStatus: summary.get(1) || 0n,
            withdrawableTON: summary.get(2) || 0n,
            withdrawableUSDT: summary.get(3) || 0n
        }
    }

    // USDT related getters
    async getTotalRaisedUSDT(provider: ContractProvider) {
        const result = await provider.get('total_raised_usdt', [])
        return result.stack.readBigNumber()
    }

    async getTotalRaisedEquivalent(provider: ContractProvider) {
        const result = await provider.get('total_raised_equivalent', [])
        return result.stack.readBigNumber()
    }

    async isUSDTEnabled(provider: ContractProvider) {
        const result = await provider.get('is_usdt_enabled', [])
        return result.stack.readBoolean()
    }
}