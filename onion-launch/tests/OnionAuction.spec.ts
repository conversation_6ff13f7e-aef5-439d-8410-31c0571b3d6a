import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';

describe('OnionAuction', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k TON
        const hardCap = toNano('2000000'); // 2M TON  
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });
    });

    it('should deploy successfully', async () => {
        // Check if auction is deployed and in pending state
        const auctionStatus = await onionAuction.getAuctionStatus();
        expect(auctionStatus.toString()).toBe('0'); // pending state
    });

    it('should start auction', async () => {
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        const startResult = await onionAuction.send(
            deployer.getSender(),
            { 
                value: toNano('0.1'),
            },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        expect(startResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if auction is now active
        const auctionStatus = await onionAuction.getAuctionStatus();
        expect(auctionStatus.toString()).toBe('1'); // active state
    });

    it('should handle purchase', async () => {
        // First start the auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // Create a buyer
        const buyer = await blockchain.treasury('buyer');
        
        // Make a purchase - increase value significantly to cover gas costs for contract deployment
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            {
                value: toNano('2.1'), // 2.1 TON (0.1 for purchase + 2 for gas and contract deployment)
            },
            {
                $$type: 'Purchase',
                amount: toNano('0.1'), // 0.1 TON amount
                currency: 0n // TON currency
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that total raised increased
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised > 0n).toBe(true);
    });

    it('should get auction info', async () => {
        const auctionInfo = await onionAuction.getAuctionInfo();
        expect(auctionInfo.soft_cap.toString()).toBe(toNano('500000').toString());
        expect(auctionInfo.hard_cap.toString()).toBe(toNano('2000000').toString());
        expect(auctionInfo.total_supply.toString()).toBe(toNano('1000000').toString());
    });

    it('should check auction status', async () => {
        const isActive = await onionAuction.getIsAuctionActive();
        expect(typeof isActive).toBe('boolean');
        
        const remainingTokens = await onionAuction.getRemainingTokens();
        expect(remainingTokens.toString()).toBe(toNano('1000000').toString()); // Should be full supply initially
    });
});
