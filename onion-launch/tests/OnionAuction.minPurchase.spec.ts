import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano, beginCell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('OnionAuction Min Purchase Configuration', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;
    let publicKeyBigInt: bigint;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');

        // Generate key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);
        publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k TON
        const hardCap = toNano('2000000'); // 2M TON
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Set signing key
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetSigningKey',
                public_key: publicKeyBigInt
            }
        );

        // Start the auction
        const startTime2 = BigInt(Math.floor(Date.now() / 1000));
        const endTime2 = startTime2 + 86400n;

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime2,
                end_time: endTime2,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );
    });

    // Helper function to create purchase calculation cell
    function createPurchaseCalculationCell(calc: any) {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .endCell();
    }

    // Helper function to sign purchase calculation
    function signPurchaseCalculation(calc: any) {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    it('should have default minimum purchase amount', async () => {
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Default 0.1 TON
    });

    it('should allow owner to set minimum purchase amount', async () => {
        const newMinPurchase = toNano('0.5'); // 0.5 TON

        const setResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: newMinPurchase
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that minimum purchase amount was updated
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(newMinPurchase.toString());
    });

    it('should reject purchase below minimum amount', async () => {
        // Set minimum purchase to 1 TON
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('1')
            }
        );

        const buyer = await blockchain.treasury('buyer');
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('0.5'); // Below minimum
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: buyer.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: 1n
        };

        const signature = signPurchaseCalculation(calculation);

        // Try to purchase with 0.5 TON (below minimum)
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('0.7') }, // 0.5 for purchase + 0.2 for gas
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should accept purchase above minimum amount', async () => {
        // Set minimum purchase to 0.5 TON
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('0.5')
            }
        );

        const buyer = await blockchain.treasury('buyer');
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('1'); // Above minimum
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: buyer.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: 2n
        };

        const signature = signPurchaseCalculation(calculation);

        // Purchase with 1 TON (above minimum)
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('1.2') }, // 1 for purchase + 0.2 for gas
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that total raised increased
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised > 0n).toBe(true);
    });

    it('should reject non-owner attempts to set minimum purchase', async () => {
        const nonOwner = await blockchain.treasury('nonOwner');
        
        const setResult = await onionAuction.send(
            nonOwner.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('2')
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: nonOwner.address,
            to: onionAuction.address,
            success: false,
        });

        // Check that minimum purchase amount was not changed
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Should still be default
    });

    it('should reject zero or negative minimum purchase amount', async () => {
        const setResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: 0n // Zero amount
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: false,
        });

        // Check that minimum purchase amount was not changed
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Should still be default
    });
});
