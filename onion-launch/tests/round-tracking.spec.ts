import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../wrappers/OnionAuction';
import { UserPurchase } from '../wrappers/UserPurchase';
import '@ton/test-utils';
import { compile } from '@ton/blueprint';

describe('Round Tracking', () => {
    let code: Cell;
    let userPurchaseCode: Cell;
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let user2: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;

    beforeAll(async () => {
        code = await compile('OnionAuction');
        userPurchaseCode = await compile('UserPurchase');
    });

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user1 = await blockchain.treasury('user1');
        user2 = await blockchain.treasury('user2');

        const startTime = Math.floor(Date.now() / 1000);
        const endTime = startTime + 86400; // 24 hours

        onionAuction = blockchain.openContract(
            OnionAuction.createFromConfig({
                owner: deployer.address,
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('1000'),
                hard_cap: toNano('10000'),
                total_supply: toNano('1000000')
            }, code)
        );

        const deployResult = await onionAuction.sendDeploy(deployer.getSender(), toNano('0.05'));
        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Start the auction
        await onionAuction.sendStartAuction(
            deployer.getSender(),
            toNano('0.05'),
            startTime,
            endTime,
            toNano('1000'),
            toNano('10000'),
            toNano('0.1') // Initial price
        );
    });

    it('should initialize first round correctly', async () => {
        // Check current round
        const currentRound = await onionAuction.getCurrentRound();
        expect(currentRound).toBe(1n);

        // Check first round stats
        const round1Stats = await onionAuction.getRoundStats(1n);
        expect(round1Stats).toBeDefined();
        if (round1Stats) {
            expect(round1Stats.round_number).toBe(1n);
            expect(round1Stats.price).toBe(toNano('0.1'));
            expect(round1Stats.total_raised_ton).toBe(0n);
            expect(round1Stats.total_raised_usdt).toBe(0n);
            expect(round1Stats.tokens_sold).toBe(0n);
            expect(round1Stats.purchase_count).toBe(0n);
            expect(round1Stats.unique_users).toBe(0n);
            expect(round1Stats.refund_count).toBe(0n);
        }
    });

    it('should track purchases in round statistics', async () => {
        // User1 makes a purchase
        const purchaseResult1 = await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('10.1'), // 10 TON + gas
            toNano('10'),
            0 // TON currency
        );

        expect(purchaseResult1.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after first purchase
        const round1Stats = await onionAuction.getRoundStats(1n);
        expect(round1Stats).toBeDefined();
        if (round1Stats) {
            expect(round1Stats.total_raised_ton).toBe(toNano('10'));
            expect(round1Stats.tokens_sold).toBe(toNano('100')); // 10 TON / 0.1 TON per token
            expect(round1Stats.purchase_count).toBe(1n);
            expect(round1Stats.unique_users).toBe(1n);
        }

        // User2 makes a purchase
        const purchaseResult2 = await onionAuction.sendPurchase(
            user2.getSender(),
            toNano('5.1'), // 5 TON + gas
            toNano('5'),
            0 // TON currency
        );

        expect(purchaseResult2.transactions).toHaveTransaction({
            from: user2.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after second purchase
        const round1StatsUpdated = await onionAuction.getRoundStats(1n);
        expect(round1StatsUpdated).toBeDefined();
        if (round1StatsUpdated) {
            expect(round1StatsUpdated.total_raised_ton).toBe(toNano('15'));
            expect(round1StatsUpdated.tokens_sold).toBe(toNano('150')); // 15 TON / 0.1 TON per token
            expect(round1StatsUpdated.purchase_count).toBe(2n);
            expect(round1StatsUpdated.unique_users).toBe(2n);
        }

        // User1 makes another purchase (should not increase unique users)
        const purchaseResult3 = await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('3.1'), // 3 TON + gas
            toNano('3'),
            0 // TON currency
        );

        expect(purchaseResult3.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after third purchase
        const round1StatsFinal = await onionAuction.getRoundStats(1n);
        expect(round1StatsFinal).toBeDefined();
        if (round1StatsFinal) {
            expect(round1StatsFinal.total_raised_ton).toBe(toNano('18'));
            expect(round1StatsFinal.tokens_sold).toBe(toNano('180'));
            expect(round1StatsFinal.purchase_count).toBe(3n);
            expect(round1StatsFinal.unique_users).toBe(2n); // Still 2 unique users
        }
    });

    it('should track user participation across rounds', async () => {
        // User1 participates in round 1
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('10.1'),
            toNano('10'),
            0
        );

        // Check user round count
        const userRoundCount = await onionAuction.getUserRoundCount(user1.address);
        expect(userRoundCount).toBe(1n);

        // Check user participated rounds
        const userRounds = await onionAuction.getUserParticipatedRounds(user1.address);
        expect(userRounds).toBeDefined();
    });

    it('should provide aggregated statistics', async () => {
        // Make purchases from different users
        await onionAuction.sendPurchase(user1.getSender(), toNano('10.1'), toNano('10'), 0);
        await onionAuction.sendPurchase(user2.getSender(), toNano('5.1'), toNano('5'), 0);

        // Get aggregated stats
        const aggregatedStats = await onionAuction.getAggregatedStats();
        expect(aggregatedStats).toBeDefined();
        
        expect(aggregatedStats.total_raised_ton).toBe(toNano('15'));
        expect(aggregatedStats.total_raised_usdt).toBe(0n);
        expect(aggregatedStats.tokens_sold).toBe(toNano('150'));
        expect(aggregatedStats.purchase_count).toBe(2n);
        expect(aggregatedStats.round_number).toBe(0n); // 0 indicates aggregated stats
    });

    it('should track total rounds correctly', async () => {
        const totalRounds = await onionAuction.getTotalRounds();
        expect(totalRounds).toBe(1n); // Should be 1 since auction just started
    });

    it('should get current round stats', async () => {
        const currentRoundStats = await onionAuction.getCurrentRoundStats();
        expect(currentRoundStats).toBeDefined();
        if (currentRoundStats) {
            expect(currentRoundStats.round_number).toBe(1n);
            expect(currentRoundStats.price).toBe(toNano('0.1'));
        }
    });

    it('should track user purchases by round in UserPurchase contract', async () => {
        // Make a purchase
        await onionAuction.sendPurchase(user1.getSender(), toNano('10.1'), toNano('10'), 0);

        // Get user purchase contract
        const userPurchaseAddress = await onionAuction.getUserPurchaseAddress(user1.address);
        const userPurchase = blockchain.openContract(
            UserPurchase.fromAddress(userPurchaseAddress)
        );

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeDefined();
        if (purchaseDetails) {
            expect(purchaseDetails.round_number).toBe(1n);
            expect(purchaseDetails.amount).toBe(toNano('10'));
            expect(purchaseDetails.currency).toBe(0n);
        }

        // Check round-specific stats
        const roundTotalAmount = await userPurchase.getRoundTotalAmount(1n);
        const roundTotalTokens = await userPurchase.getRoundTotalTokens(1n);
        const roundPurchaseCount = await userPurchase.getRoundPurchaseCount(1n);

        expect(roundTotalAmount).toBe(toNano('10'));
        expect(roundTotalTokens).toBe(toNano('100'));
        expect(roundPurchaseCount).toBe(1n);

        // Check participated rounds
        const participatedRounds = await userPurchase.getParticipatedRounds();
        expect(participatedRounds).toBeDefined();

        const roundCount = await userPurchase.getRoundCount();
        expect(roundCount).toBe(1n);
    });
});
