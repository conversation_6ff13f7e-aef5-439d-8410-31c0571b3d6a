import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../wrappers/OnionAuction';
import '@ton/test-utils';
import { compile } from '@ton/blueprint';

describe('Fund Withdrawal', () => {
    let code: Cell;
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let treasury: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;

    beforeAll(async () => {
        code = await compile('OnionAuction');
    });

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        treasury = await blockchain.treasury('treasury');
        user1 = await blockchain.treasury('user1');

        const startTime = Math.floor(Date.now() / 1000);
        const endTime = startTime + 86400; // 24 hours

        onionAuction = blockchain.openContract(
            OnionAuction.createFromConfig({
                owner: deployer.address,
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('100'), // Low soft cap for testing
                hard_cap: toNano('1000'),
                total_supply: toNano('1000000')
            }, code)
        );

        const deployResult = await onionAuction.sendDeploy(deployer.getSender(), toNano('0.05'));
        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Start the auction
        await onionAuction.sendStartAuction(
            deployer.getSender(),
            toNano('0.05'),
            startTime,
            endTime,
            toNano('100'),
            toNano('1000'),
            toNano('0.1') // Initial price
        );
    });

    it('should set treasury address', async () => {
        const setTreasuryResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetTreasury',
                treasury_address: treasury.address
            }
        );

        expect(setTreasuryResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check treasury address was set
        const treasuryAddress = await onionAuction.getTreasuryAddress();
        expect(treasuryAddress?.toString()).toBe(treasury.address.toString());
    });

    it('should not allow withdrawal before auction ends successfully', async () => {
        // Make a purchase to have some funds
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('10.1'), // 10 TON + gas
            toNano('10'),
            0 // TON currency
        );

        // Try to withdraw before auction ends
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('5'),
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: false, // Should fail because auction is still active
        });
    });

    it('should allow withdrawal after successful auction', async () => {
        // Make enough purchases to reach soft cap
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('50.1'), // 50 TON + gas
            toNano('50'),
            0 // TON currency
        );

        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('60.1'), // 60 TON + gas
            toNano('60'),
            0 // TON currency
        );

        // End the auction manually (simulate time passing)
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Check auction status is successful
        const auctionStatus = await onionAuction.getAuctionStatus();
        expect(auctionStatus).toBe(2); // ended_success

        // Check withdrawable amounts
        const withdrawableTON = await onionAuction.getWithdrawableTON();
        expect(withdrawableTON).toBe(toNano('110')); // 50 + 60 TON

        const canWithdraw = await onionAuction.canWithdraw();
        expect(canWithdraw).toBe(true);

        // Withdraw partial amount
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('50'),
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        expect(withdrawResult.transactions).toHaveTransaction({
            from: onionAuction.address,
            to: treasury.address,
            value: toNano('50'),
        });

        // Check remaining withdrawable amount
        const remainingWithdrawable = await onionAuction.getWithdrawableTON();
        expect(remainingWithdrawable).toBe(toNano('60')); // 110 - 50
    });

    it('should allow withdrawing all funds at once', async () => {
        // Make purchases to reach soft cap
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('120.1'), // 120 TON + gas
            toNano('120'),
            0 // TON currency
        );

        // End the auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Withdraw all funds (amount = 0 means withdraw all)
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: 0n, // Withdraw all
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        expect(withdrawResult.transactions).toHaveTransaction({
            from: onionAuction.address,
            to: treasury.address,
            value: toNano('120'),
        });

        // Check no funds remaining
        const remainingWithdrawable = await onionAuction.getWithdrawableTON();
        expect(remainingWithdrawable).toBe(0n);
    });

    it('should not allow non-owner to withdraw funds', async () => {
        // Make purchases and end auction
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('120.1'),
            toNano('120'),
            0
        );

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Try to withdraw as non-owner
        const withdrawResult = await onionAuction.send(
            user1.getSender(), // Non-owner
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('50'),
                destination: user1.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: false, // Should fail because user1 is not owner
        });
    });

    it('should not allow withdrawing more than available', async () => {
        // Make purchases and end auction
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('120.1'),
            toNano('120'),
            0
        );

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Try to withdraw more than available
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('200'), // More than the 120 TON available
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: false, // Should fail due to insufficient balance
        });
    });

    it('should provide correct withdrawal summary', async () => {
        // Make purchases and end auction
        await onionAuction.sendPurchase(
            user1.getSender(),
            toNano('120.1'),
            toNano('120'),
            0
        );

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Get withdrawal summary
        const summary = await onionAuction.getWithdrawalSummary();
        expect(summary.auctionStatus).toBe(2n); // ended_success
        expect(summary.withdrawableTON).toBe(toNano('120'));
        expect(summary.withdrawableUSDT).toBe(0n); // No USDT in this test
    });
});
