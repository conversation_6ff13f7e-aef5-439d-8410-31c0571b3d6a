import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';

describe('OnionAuction USDT Integration', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let buyer: SandboxContract<TreasuryContract>;
    let usdtMaster: SandboxContract<TreasuryContract>;
    let usdtWallet: SandboxContract<TreasuryContract>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        buyer = await blockchain.treasury('buyer');
        usdtMaster = await blockchain.treasury('usdt_master');
        usdtWallet = await blockchain.treasury('usdt_wallet');

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k TON
        const hardCap = toNano('2000000'); // 2M TON  
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });
    });

    it('should set USDT configuration', async () => {
        const setUSDTResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        expect(setUSDTResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if USDT is enabled
        const isUSDTEnabled = await onionAuction.getIsUsdtEnabled();
        expect(isUSDTEnabled).toBe(true);

        // Check USDT config
        const usdtConfig = await onionAuction.getUsdtConfig();
        expect(usdtConfig?.master_address.toString()).toBe(usdtMaster.address.toString());
        expect(usdtConfig?.wallet_address?.toString()).toBe(usdtWallet.address.toString());
        expect(usdtConfig?.decimals.toString()).toBe('6');
    });

    // Direct USDT purchase test removed - all USDT purchases must include signature verification
    // See OnionAuction.usdt.signature.spec.ts for signature-verified USDT purchase tests

    it('should reject USDT transfer without signature verification', async () => {
        // Set USDT configuration
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        // Start auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // Try to send USDT without signature verification (should fail)
        const purchaseResult = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: 50000000n,
                sender: buyer.address,
                forward_payload: null // No signature verification data
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: false,
        });
    });

    // Mixed TON and USDT purchase test removed - all purchases must be signature-verified
    // See integration.signature.spec.ts for tests with multiple signature-verified purchases
});
