import { toNano, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider } from '@ton/blueprint';
import { on } from 'events';

export async function run(provider: NetworkProvider) {
    console.log('🚀 Deploying OnionAuction with USDT support...');
    
    // Auction parameters
    const startTime = BigInt(+new Date(2025, 6, 15) / 1000); // Start on July 15, 2025
    const endTime = startTime + 86400n * 7n; // Run for 7 days
    const softCap = toNano('500000'); // 500k TON soft cap
    const hardCap = toNano('2000000'); // 2M TON hard cap
    const totalSupply = toNano('1000000'); // 1M tokens total supply

    // Deploy the auction contract
    const onionAuction = provider.open(
        await OnionAuction.fromInit(
            provider.sender().address!,
            startTime,
            endTime,
            softCap,
            hardCap,
            totalSupply
        )
    );

    await onionAuction.send(
        provider.sender(),
        {            value: toNano('0.5'), // Initial deployment fee
        }, {
            $$type: 'StartAuction',
            start_time: startTime,
            end_time: endTime,
            soft_cap: softCap,
            hard_cap: hardCap,
            initial_price: toNano('0.1') // Initial price per token
        }
    );

    console.log('OnionAuction going to deploy to:', onionAuction.address.toString());
    await onionAuction.send(
        provider.sender(),
        {
            value: toNano('0.5'),
        },
        'Deploy'
    );

    await provider.waitForDeploy(onionAuction.address);
    console.log('✅ OnionAuction deployed at:', onionAuction.address.toString());

    // USDT configuration (Mainnet USDT addresses)
    const USDT_MASTER_MAINNET = 'EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs'; // Tether USD on TON
    const USDT_MASTER_TESTNET = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN'; // Test USDT

    // Choose the appropriate USDT master address based on network
    const isMainnet = provider.network() === 'mainnet';
    const usdtMasterAddress = isMainnet ? USDT_MASTER_MAINNET : USDT_MASTER_TESTNET;

    console.log(`🔧 Configuring USDT support for ${isMainnet ? 'mainnet' : 'testnet'}...`);
    console.log('USDT Master Address:', usdtMasterAddress);

    // Calculate the USDT wallet address for the auction contract
    // Note: In a real deployment, you would call the USDT master contract's get_wallet_address method
    // For this example, we'll use a placeholder calculation
    
    // This is a simplified approach - in production, you should:
    // 1. Call the USDT master contract's get_wallet_address method
    // 2. Or deploy the USDT wallet first and get its address
    
    console.log('⚠️  Note: You need to calculate the correct USDT wallet address');
    console.log('   Call USDT master contract get_wallet_address method with auction address');
    console.log('   Auction address:', onionAuction.address.toString());

    const placeholderUSDTWallet = 'kQAhz3Vorq3uIenecN3Bo8WRD5_hytdr7y2vPhE1D4h2BGKM'; // null address as placeholder

    try {
        // Set USDT configuration
        await onionAuction.send(
            provider.sender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: Address.parse(usdtMasterAddress),
                usdt_wallet: Address.parse(placeholderUSDTWallet)
            }
        );

        console.log('✅ USDT configuration set successfully');
        console.log('⚠️  Remember to update the USDT wallet address with the correct one!');

    } catch (error) {
        console.log('❌ Failed to set USDT configuration:', error);
        console.log('💡 You can set it later using the SetUSDTAddress message');
    }

    // Wait 5 seconds before continuing
    console.log('⏳ Waiting 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    try {
        // Set USDT configuration
        await onionAuction.send(
            provider.sender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetSigningKey',
                public_key: BigInt('0x3f23bbe305ca208fd2d2bff11c503c1b4f53f676f1a5dd74d558ef478ee7ed7f')
            }
        );

        console.log('✅ signing key set successfully');
        console.log('⚠️  Remember to update the signing key with the correct one!');

    } catch (error) {
        console.log('❌ Failed to set signing key:', error);
        console.log('💡 You can set it later using the SetSigningKey message');
    }

    // Display auction information
    console.log('\n📊 Auction Information:');
    console.log('Contract Address:', onionAuction.address.toString());
    console.log('Start Time:', new Date(Number(startTime) * 1000).toISOString());
    console.log('End Time:', new Date(Number(endTime) * 1000).toISOString());
    console.log('Soft Cap:', (Number(softCap) / 1e9).toLocaleString(), 'TON');
    console.log('Hard Cap:', (Number(hardCap) / 1e9).toLocaleString(), 'TON');
    console.log('Total Supply:', (Number(totalSupply) / 1e9).toLocaleString(), 'tokens');

    // Instructions for next steps
    console.log('\n📋 Next Steps:');
    console.log('1. Calculate the correct USDT wallet address for the auction contract');
    console.log('2. Update USDT configuration with the correct wallet address');
    console.log('3. Start the auction using StartAuction message');
    console.log('4. Test both TON and USDT purchases');

    // Example commands
    console.log('\n💡 Example commands:');
    console.log('// Get USDT wallet address (call this on USDT master contract):');
    console.log(`get_wallet_address(${onionAuction.address.toString()})`);
    console.log('');
    console.log('// Update USDT configuration:');
    console.log('SetUSDTAddress { usdt_master: "' + usdtMasterAddress + '", usdt_wallet: "CALCULATED_WALLET_ADDRESS" }');
    console.log('');
    console.log('// Start auction:');
    console.log('StartAuction {');
    console.log('  start_time: ' + startTime + ',');
    console.log('  end_time: ' + endTime + ',');
    console.log('  soft_cap: ' + softCap + ',');
    console.log('  hard_cap: ' + hardCap + ',');
    console.log('  initial_price: ' + toNano('0.1') + '');
    console.log('}');

    return onionAuction.address;
}

// Helper function to get USDT wallet address (to be called separately)
export async function getUSDTWalletAddress(
    provider: NetworkProvider,
    usdtMasterAddress: string,
    ownerAddress: string
): Promise<string> {
    console.log('🔍 Getting USDT wallet address...');
    console.log('USDT Master:', usdtMasterAddress);
    console.log('Owner:', ownerAddress);

    // This would typically involve calling the USDT master contract
    // For now, we'll return a placeholder
    console.log('⚠️  This is a placeholder implementation');
    console.log('   In production, call the USDT master contract\'s get_wallet_address method');
    
    return 'EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAm'; // placeholder
}

// Helper function to configure USDT after deployment
export async function configureUSDT(
    provider: NetworkProvider,
    auctionAddress: string,
    usdtMasterAddress: string,
    usdtWalletAddress: string
) {
    console.log('🔧 Configuring USDT for existing auction...');
    
    const onionAuction = provider.open(
        OnionAuction.fromAddress(Address.parse(auctionAddress))
    );

    await onionAuction.send(
        provider.sender(),
        {
            value: toNano('0.1'),
        },
        {
            $$type: 'SetUSDTAddress',
            usdt_master: Address.parse(usdtMasterAddress),
            usdt_wallet: Address.parse(usdtWalletAddress)
        }
    );

    console.log('✅ USDT configuration updated successfully');
}
